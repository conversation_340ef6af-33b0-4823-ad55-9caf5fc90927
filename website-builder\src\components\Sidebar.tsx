import { 
  MousePointer,
  Type, 
  Image, 
  Square,
  Circle,
  Triangle,
  Layout,
  Grid,
  List
} from 'lucide-react'

export type Tool = 'pointer' | 'text' | 'image' | 'box' | 'circle' | 'triangle'

interface SidebarProps {
  selectedTool: Tool
  onToolSelect: (tool: Tool) => void
  onComponentDrag: (componentType: string) => void
}

const tools = [
  { id: 'pointer' as Tool, icon: MousePointer, label: 'Select' },
  { id: 'text' as Tool, icon: Type, label: 'Text' },
  { id: 'image' as Tool, icon: Image, label: 'Image' },
  { id: 'box' as Tool, icon: Square, label: 'Box' },
  { id: 'circle' as Tool, icon: Circle, label: 'Circle' },
  { id: 'triangle' as Tool, icon: Triangle, label: 'Triangle' },
]

const components = [
  { id: 'header', icon: Layout, label: 'Header', description: 'Navigation header' },
  { id: 'hero', icon: Grid, label: 'Hero Section', description: 'Main banner area' },
  { id: 'button', icon: Square, label: 'Button', description: 'Call-to-action button' },
  { id: 'card', icon: Square, label: 'Card', description: 'Content card' },
  { id: 'list', icon: List, label: 'List', description: 'Ordered or unordered list' },
  { id: 'footer', icon: Layout, label: 'Footer', description: 'Page footer' },
]

export function Sidebar({ selectedTool, onToolSelect, onComponentDrag }: SidebarProps) {
  return (
    <div className="w-64 bg-white border-r border-gray-200 flex flex-col">
      {/* Tools Section */}
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-sm font-medium text-gray-800 mb-3">Tools</h2>
        <div className="grid grid-cols-2 gap-2">
          {tools.map((tool) => {
            const Icon = tool.icon
            return (
              <button 
                key={tool.id}
                onClick={() => onToolSelect(tool.id)}
                className={`p-3 rounded border-2 flex flex-col items-center space-y-1 text-xs transition-colors ${
                  selectedTool === tool.id 
                    ? 'border-blue-500 bg-blue-50 text-blue-700' 
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                }`}
              >
                <Icon size={20} />
                <span>{tool.label}</span>
              </button>
            )
          })}
        </div>
      </div>
      
      {/* Components Section */}
      <div className="p-4 flex-1 overflow-y-auto">
        <h2 className="text-sm font-medium text-gray-800 mb-3">Components</h2>
        <div className="space-y-2">
          {components.map((component) => {
            const Icon = component.icon
            return (
              <div
                key={component.id}
                draggable
                onDragStart={(e) => {
                  e.dataTransfer.setData('application/json', component.id)
                  onComponentDrag(component.id)
                }}
                className="p-3 border border-gray-200 rounded cursor-pointer hover:border-gray-300 hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center space-x-2 mb-1">
                  <Icon size={16} className="text-gray-500" />
                  <div className="text-sm font-medium">{component.label}</div>
                </div>
                <div className="text-xs text-gray-500">{component.description}</div>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}
