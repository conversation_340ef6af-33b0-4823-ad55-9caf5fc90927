import { useState } from 'react'
import { MenuBar } from './components/MenuBar'
import { Sidebar, Tool } from './components/Sidebar'
import { Canvas, DeviceType } from './components/Canvas'
import { PropertiesPanel } from './components/PropertiesPanel'
import './App.css'

function App() {
  const [selectedTool, setSelectedTool] = useState<Tool>('pointer')
  const [currentDevice, setCurrentDevice] = useState<DeviceType>('desktop')
  const [selectedElement, setSelectedElement] = useState<string | null>(null)
  const [currentProject, setCurrentProject] = useState<string | null>(null)

  const handleNewProject = () => {
    console.log('Creating new project...')
    setCurrentProject(null)
    setSelectedElement(null)
  }

  const handleOpenProject = () => {
    console.log('Opening project...')
    // TODO: Implement file dialog
  }

  const handleSaveProject = () => {
    console.log('Saving project...')
    // TODO: Implement save functionality
  }

  const handleExportProject = () => {
    console.log('Exporting project...')
    // TODO: Implement export functionality
  }

  const handleDeviceChange = (device: DeviceType) => {
    setCurrentDevice(device)
  }

  const handleComponentDrag = (componentType: string) => {
    // Set up drag data
    const dragData = componentType
    // This will be handled by the Canvas component
  }

  const handleElementUpdate = (elementId: string, updates: any) => {
    console.log('Updating element:', elementId, updates)
    // TODO: Implement element update functionality
  }

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      <MenuBar
        onNewProject={handleNewProject}
        onOpenProject={handleOpenProject}
        onSaveProject={handleSaveProject}
        onExportProject={handleExportProject}
        onDeviceChange={handleDeviceChange}
      />

      <div className="flex flex-1 overflow-hidden">
        <Sidebar
          selectedTool={selectedTool}
          onToolSelect={setSelectedTool}
          onComponentDrag={handleComponentDrag}
        />

        <Canvas
          device={currentDevice}
          onElementSelect={setSelectedElement}
        />

        <PropertiesPanel
          selectedElement={selectedElement}
          onElementUpdate={handleElementUpdate}
        />
      </div>
    </div>
  )
}

export default App
