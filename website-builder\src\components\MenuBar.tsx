import { 
  File, 
  FolderOpen, 
  Save, 
  Download, 
  Settings, 
  Smartphone,
  Tablet,
  Monitor
} from 'lucide-react'

interface MenuBarProps {
  onNewProject: () => void
  onOpenProject: () => void
  onSaveProject: () => void
  onExportProject: () => void
  onDeviceChange: (device: 'mobile' | 'tablet' | 'desktop') => void
}

export function MenuBar({ 
  onNewProject, 
  onOpenProject, 
  onSaveProject, 
  onExportProject,
  onDeviceChange 
}: MenuBarProps) {
  return (
    <div className="bg-white border-b border-gray-200 px-4 py-2 flex items-center justify-between">
      <div className="flex items-center space-x-4">
        <h1 className="text-lg font-semibold text-gray-800">Website Builder</h1>
        <div className="flex items-center space-x-2">
          <button 
            onClick={onNewProject}
            className="flex items-center space-x-1 px-3 py-1 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded"
          >
            <File size={16} />
            <span>New</span>
          </button>
          <button 
            onClick={onOpenProject}
            className="flex items-center space-x-1 px-3 py-1 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded"
          >
            <FolderOpen size={16} />
            <span>Open</span>
          </button>
          <button 
            onClick={onSaveProject}
            className="flex items-center space-x-1 px-3 py-1 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded"
          >
            <Save size={16} />
            <span>Save</span>
          </button>
          <button 
            onClick={onExportProject}
            className="flex items-center space-x-1 px-3 py-1 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded"
          >
            <Download size={16} />
            <span>Export</span>
          </button>
        </div>
      </div>
      
      {/* Device Preview Controls */}
      <div className="flex items-center space-x-2">
        <button 
          onClick={() => onDeviceChange('mobile')}
          className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded"
          title="Mobile View"
        >
          <Smartphone size={18} />
        </button>
        <button 
          onClick={() => onDeviceChange('tablet')}
          className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded"
          title="Tablet View"
        >
          <Tablet size={18} />
        </button>
        <button 
          onClick={() => onDeviceChange('desktop')}
          className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded"
          title="Desktop View"
        >
          <Monitor size={18} />
        </button>
        <button className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded">
          <Settings size={18} />
        </button>
      </div>
    </div>
  )
}
