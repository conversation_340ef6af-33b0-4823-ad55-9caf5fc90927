import { useState } from 'react'
import { Rnd } from 'react-rnd'

export type DeviceType = 'mobile' | 'tablet' | 'desktop'

interface CanvasProps {
  device: DeviceType
  onElementSelect: (elementId: string | null) => void
}

const deviceSizes = {
  mobile: { width: 375, height: 667 },
  tablet: { width: 768, height: 1024 },
  desktop: { width: 1024, height: 768 }
}

export function Canvas({ device, onElementSelect }: CanvasProps) {
  const [elements, setElements] = useState<any[]>([])
  const [selectedElement, setSelectedElement] = useState<string | null>(null)

  const canvasSize = deviceSizes[device]

  const updateElement = (elementId: string, updates: any) => {
    setElements(prev => prev.map(el =>
      el.id === elementId ? { ...el, ...updates } : el
    ))
  }
  
  const handleCanvasClick = (e: React.MouseEvent) => {
    // If clicking on the canvas background, deselect all elements
    if (e.target === e.currentTarget) {
      setSelectedElement(null)
      onElementSelect(null)
    }
  }
  
  const handleElementClick = (elementId: string, e: React.MouseEvent) => {
    e.stopPropagation()
    setSelectedElement(elementId)
    onElementSelect(elementId)
  }
  
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    const componentType = e.dataTransfer.getData('application/json')

    if (componentType) {
      const rect = e.currentTarget.getBoundingClientRect()
      const x = e.clientX - rect.left
      const y = e.clientY - rect.top

      const newElement = {
        id: `${componentType}-${Date.now()}`,
        type: componentType,
        x: Math.max(0, x - 100), // Center the element on cursor
        y: Math.max(0, y - 50),
        width: getDefaultSize(componentType).width,
        height: getDefaultSize(componentType).height,
        content: getDefaultContent(componentType)
      }

      setElements([...elements, newElement])
    }
  }
  
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
  }
  
  return (
    <div className="flex-1 bg-gray-100 p-8 overflow-auto">
      <div className="flex justify-center">
        <div 
          className="bg-white shadow-lg relative border"
          style={{ 
            width: canvasSize.width, 
            minHeight: canvasSize.height,
            transition: 'all 0.3s ease'
          }}
          onClick={handleCanvasClick}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
        >
          {elements.length === 0 ? (
            <div className="absolute inset-0 flex items-center justify-center text-gray-500">
              <div className="text-center">
                <div className="text-lg font-medium mb-2">Welcome to Website Builder</div>
                <div className="text-sm">Start by dragging components from the sidebar</div>
                <div className="text-xs mt-2">Current view: {device}</div>
              </div>
            </div>
          ) : (
            elements.map((element) => (
              <Rnd
                key={element.id}
                size={{ width: element.width, height: element.height }}
                position={{ x: element.x, y: element.y }}
                onDragStop={(_e, d) => {
                  updateElement(element.id, { x: d.x, y: d.y })
                }}
                onResizeStop={(_e, _direction, ref, _delta, position) => {
                  updateElement(element.id, {
                    width: parseInt(ref.style.width),
                    height: parseInt(ref.style.height),
                    x: position.x,
                    y: position.y,
                  })
                }}
                bounds="parent"
                className={`${
                  selectedElement === element.id
                    ? 'border-2 border-blue-500'
                    : 'border border-transparent hover:border-gray-300'
                }`}
                onClick={(e) => handleElementClick(element.id, e)}
                enableResizing={selectedElement === element.id}
                disableDragging={selectedElement !== element.id}
              >
                <div className="p-2 h-full flex items-center justify-center bg-white rounded">
                  {renderElementContent(element)}
                </div>

                {selectedElement === element.id && (
                  <div className="absolute -top-6 left-0 bg-blue-500 text-white text-xs px-2 py-1 rounded z-10">
                    {element.type}
                  </div>
                )}
              </Rnd>
            ))
          )}
        </div>
      </div>
    </div>
  )
}

function getDefaultSize(componentType: string): { width: number; height: number } {
  switch (componentType) {
    case 'header':
      return { width: 400, height: 80 }
    case 'hero':
      return { width: 500, height: 200 }
    case 'button':
      return { width: 120, height: 40 }
    case 'card':
      return { width: 300, height: 200 }
    case 'list':
      return { width: 250, height: 150 }
    case 'footer':
      return { width: 400, height: 60 }
    default:
      return { width: 200, height: 100 }
  }
}

function getDefaultContent(componentType: string): any {
  switch (componentType) {
    case 'header':
      return { text: 'Header', links: ['Home', 'About', 'Contact'] }
    case 'hero':
      return { title: 'Hero Title', subtitle: 'Hero subtitle text' }
    case 'button':
      return { text: 'Click Me', variant: 'primary' }
    case 'card':
      return { title: 'Card Title', content: 'Card content goes here' }
    case 'list':
      return { items: ['Item 1', 'Item 2', 'Item 3'] }
    case 'footer':
      return { text: 'Footer content' }
    default:
      return { text: componentType }
  }
}

function renderElementContent(element: any) {
  switch (element.type) {
    case 'header':
      return (
        <div className="w-full">
          <div className="font-bold text-lg">{element.content.text}</div>
          <div className="flex space-x-4 text-sm mt-1">
            {element.content.links.map((link: string, i: number) => (
              <span key={i} className="text-blue-600">{link}</span>
            ))}
          </div>
        </div>
      )
    case 'hero':
      return (
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-2">{element.content.title}</h1>
          <p className="text-gray-600">{element.content.subtitle}</p>
        </div>
      )
    case 'button':
      return (
        <button className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
          {element.content.text}
        </button>
      )
    case 'card':
      return (
        <div className="border rounded p-4">
          <h3 className="font-semibold mb-2">{element.content.title}</h3>
          <p className="text-sm text-gray-600">{element.content.content}</p>
        </div>
      )
    case 'list':
      return (
        <ul className="list-disc list-inside">
          {element.content.items.map((item: string, i: number) => (
            <li key={i} className="text-sm">{item}</li>
          ))}
        </ul>
      )
    case 'footer':
      return (
        <div className="text-center text-sm text-gray-600">
          {element.content.text}
        </div>
      )
    default:
      return <div className="text-sm">{element.content.text}</div>
  }
}
