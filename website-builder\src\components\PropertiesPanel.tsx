import { useState } from 'react'

interface PropertiesPanelProps {
  selectedElement: string | null
  onElementUpdate: (elementId: string, updates: any) => void
}

export function PropertiesPanel({ selectedElement, onElementUpdate }: PropertiesPanelProps) {
  const [properties, setProperties] = useState({
    width: 200,
    height: 100,
    backgroundColor: '#ffffff',
    color: '#000000',
    fontSize: 16,
    fontWeight: 'normal',
    textAlign: 'left',
    padding: 8,
    margin: 0,
    borderRadius: 0,
    borderWidth: 0,
    borderColor: '#000000'
  })

  const handlePropertyChange = (property: string, value: any) => {
    const newProperties = { ...properties, [property]: value }
    setProperties(newProperties)
    
    if (selectedElement) {
      onElementUpdate(selectedElement, { [property]: value })
    }
  }

  if (!selectedElement) {
    return (
      <div className="w-64 bg-white border-l border-gray-200 p-4">
        <h2 className="text-sm font-medium text-gray-800 mb-3">Properties</h2>
        <div className="text-sm text-gray-500">
          Select an element to edit its properties
        </div>
      </div>
    )
  }

  return (
    <div className="w-64 bg-white border-l border-gray-200 p-4 overflow-y-auto">
      <h2 className="text-sm font-medium text-gray-800 mb-4">Properties</h2>
      
      {/* Layout Properties */}
      <div className="mb-6">
        <h3 className="text-xs font-medium text-gray-700 mb-2 uppercase tracking-wide">Layout</h3>
        <div className="space-y-3">
          <div>
            <label className="block text-xs text-gray-600 mb-1">Width</label>
            <input
              type="number"
              value={properties.width}
              onChange={(e) => handlePropertyChange('width', parseInt(e.target.value))}
              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:border-blue-500"
            />
          </div>
          <div>
            <label className="block text-xs text-gray-600 mb-1">Height</label>
            <input
              type="number"
              value={properties.height}
              onChange={(e) => handlePropertyChange('height', parseInt(e.target.value))}
              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:border-blue-500"
            />
          </div>
        </div>
      </div>

      {/* Appearance Properties */}
      <div className="mb-6">
        <h3 className="text-xs font-medium text-gray-700 mb-2 uppercase tracking-wide">Appearance</h3>
        <div className="space-y-3">
          <div>
            <label className="block text-xs text-gray-600 mb-1">Background Color</label>
            <div className="flex space-x-2">
              <input
                type="color"
                value={properties.backgroundColor}
                onChange={(e) => handlePropertyChange('backgroundColor', e.target.value)}
                className="w-8 h-8 border border-gray-300 rounded cursor-pointer"
              />
              <input
                type="text"
                value={properties.backgroundColor}
                onChange={(e) => handlePropertyChange('backgroundColor', e.target.value)}
                className="flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:border-blue-500"
              />
            </div>
          </div>
          <div>
            <label className="block text-xs text-gray-600 mb-1">Text Color</label>
            <div className="flex space-x-2">
              <input
                type="color"
                value={properties.color}
                onChange={(e) => handlePropertyChange('color', e.target.value)}
                className="w-8 h-8 border border-gray-300 rounded cursor-pointer"
              />
              <input
                type="text"
                value={properties.color}
                onChange={(e) => handlePropertyChange('color', e.target.value)}
                className="flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:border-blue-500"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Typography Properties */}
      <div className="mb-6">
        <h3 className="text-xs font-medium text-gray-700 mb-2 uppercase tracking-wide">Typography</h3>
        <div className="space-y-3">
          <div>
            <label className="block text-xs text-gray-600 mb-1">Font Size</label>
            <input
              type="number"
              value={properties.fontSize}
              onChange={(e) => handlePropertyChange('fontSize', parseInt(e.target.value))}
              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:border-blue-500"
            />
          </div>
          <div>
            <label className="block text-xs text-gray-600 mb-1">Font Weight</label>
            <select
              value={properties.fontWeight}
              onChange={(e) => handlePropertyChange('fontWeight', e.target.value)}
              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:border-blue-500"
            >
              <option value="normal">Normal</option>
              <option value="bold">Bold</option>
              <option value="lighter">Lighter</option>
              <option value="bolder">Bolder</option>
            </select>
          </div>
          <div>
            <label className="block text-xs text-gray-600 mb-1">Text Align</label>
            <select
              value={properties.textAlign}
              onChange={(e) => handlePropertyChange('textAlign', e.target.value)}
              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:border-blue-500"
            >
              <option value="left">Left</option>
              <option value="center">Center</option>
              <option value="right">Right</option>
              <option value="justify">Justify</option>
            </select>
          </div>
        </div>
      </div>

      {/* Spacing Properties */}
      <div className="mb-6">
        <h3 className="text-xs font-medium text-gray-700 mb-2 uppercase tracking-wide">Spacing</h3>
        <div className="space-y-3">
          <div>
            <label className="block text-xs text-gray-600 mb-1">Padding</label>
            <input
              type="number"
              value={properties.padding}
              onChange={(e) => handlePropertyChange('padding', parseInt(e.target.value))}
              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:border-blue-500"
            />
          </div>
          <div>
            <label className="block text-xs text-gray-600 mb-1">Margin</label>
            <input
              type="number"
              value={properties.margin}
              onChange={(e) => handlePropertyChange('margin', parseInt(e.target.value))}
              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:border-blue-500"
            />
          </div>
        </div>
      </div>

      {/* Border Properties */}
      <div className="mb-6">
        <h3 className="text-xs font-medium text-gray-700 mb-2 uppercase tracking-wide">Border</h3>
        <div className="space-y-3">
          <div>
            <label className="block text-xs text-gray-600 mb-1">Border Radius</label>
            <input
              type="number"
              value={properties.borderRadius}
              onChange={(e) => handlePropertyChange('borderRadius', parseInt(e.target.value))}
              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:border-blue-500"
            />
          </div>
          <div>
            <label className="block text-xs text-gray-600 mb-1">Border Width</label>
            <input
              type="number"
              value={properties.borderWidth}
              onChange={(e) => handlePropertyChange('borderWidth', parseInt(e.target.value))}
              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:border-blue-500"
            />
          </div>
          <div>
            <label className="block text-xs text-gray-600 mb-1">Border Color</label>
            <div className="flex space-x-2">
              <input
                type="color"
                value={properties.borderColor}
                onChange={(e) => handlePropertyChange('borderColor', e.target.value)}
                className="w-8 h-8 border border-gray-300 rounded cursor-pointer"
              />
              <input
                type="text"
                value={properties.borderColor}
                onChange={(e) => handlePropertyChange('borderColor', e.target.value)}
                className="flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:border-blue-500"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
